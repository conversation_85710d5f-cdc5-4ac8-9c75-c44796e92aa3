# to_order 函数设计文档

## 1. 函数概述

`to_order` 函数是量化交易系统中的核心订单下发API，用于向交易系统提交新的交易订单。该函数支持外汇、固收、贵金属等多种金融产品的交易，提供了丰富的订单类型和参数配置选项，能够满足从简单市价单到复杂算法交易的各种需求。

### 主要功能
- 支持多种订单类型（限价单、市价单、止损单等）
- 提供完整的风控参数配置
- 支持止损止盈功能
- 兼容外汇、固收、贵金属等多个市场
- 提供灵活的时效性控制

## 2. 技术规格

### 函数签名

```python
def to_order(
    symbol, side, 
    price=None, quantity=None, effect=None, 
    order_type=2, in_out_market=2, channel_code=None,
    time_in_force=1, expire_time=None, hedge_flag='1', 
    intention=None, warn_price=None, stop_price=None,
    value_date=None, maturity_date=None, close_order_id=None, 
    pos_type=None, currency=None, bond_quote_type=9,
    stop_loss_price=None, take_profit_price=None, bp=None
) -> str
```

### 参数分类

#### 必选参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| `symbol` | str | 合约唯一代码，标识要交易的金融产品 |
| `side` | str | 交易方向：'B'(买入) 或 'S'(卖出) |

#### 核心交易参数
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `price` | float | None | 报单价格（限价单必须，点击单可选） |
| `quantity` | float | None | 报单数量（外汇为交易量，贵金属为手数） |
| `effect` | int | None | 开平仓类型：0-中性，1-开仓，2-平仓，3-平今，4-平昨 |
| `order_type` | int | 2 | 订单类型：0-点击单，2-限价单，15-SOR单等 |

#### 市场和渠道参数
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `in_out_market` | int | 2 | 执行市场：1-内部，2-外部，3-内外部 |
| `channel_code` | str | None | 交易渠道代码 |

### 返回值
- **类型**: `str`
- **成功**: 返回订单唯一ID字符串
- **失败**: 返回 `None` 或 `'0'`，需查看策略日志获取失败原因

## 3. 业务场景

### 适用市场
- **外汇市场**: 支持主要货币对交易，包括择优询价单等外汇专用功能
- **固收市场**: 支持债券交易，提供连续匹配和集中匹配报价方式
- **贵金属市场**: 支持黄金、白银等贵金属交易，数量单位为手数
- **期货市场**: 支持投机套保标识等期货专用参数

### 交易场景
- **日内交易**: 使用GFD时效性，当日有效
- **长期持仓**: 使用GTC时效性，撤销前一直有效
- **算法交易**: 支持SOR单和容忍滑点设置
- **风险管理**: 提供止损止盈功能
- **做市交易**: 支持点击单和档位量匹配

## 4. 参数详解

### 时效性参数组
```python
time_in_force: int = 1  # 订单时效性
expire_time: str = None  # 过期时间
```

**time_in_force 取值说明**:
- `1`: GTC (Good Till Cancelled) - 撤销前一直有效
- `4`: FOK (Fill Or Kill) - 极短时间全部成交，否则全部撤销
- `5`: FAK (Fill And Kill) - 极短时间成交，剩余量全部撤销
- `6`: GFD (Good For Day) - 当日有效
- `7`: GTD (Good Till Date) - 指定日期前有效，需设置expire_time

**expire_time 格式**:
- GTC时: `YYYYMMDDHHmmss` (如: "20241231235959")
- GFD时: `HHmmss` (如: "153000")

### 止损止盈参数组
```python
warn_price: float = None      # 止损预警价
stop_price: float = None      # 止损价
stop_loss_price: float = None # 止损平仓价
take_profit_price: float = None # 止盈平仓价
```

**使用场景**:
- `warn_price`: 止损限价单的预警触发价格
- `stop_price`: 传统止损单的触发价格
- `stop_loss_price`: 开仓后的止损平仓价格
- `take_profit_price`: 开仓后的止盈平仓价格

### 日期参数组
```python
value_date: str = None     # 起息日/近端交割日
maturity_date: str = None  # 到期日/远端交割日
```

**适用场景**:
- **掉期交易**: 需要设置近端和远端交割日
- **债券交易**: 需要设置到期日
- **格式**: `yyyyMMdd` (如: "20241225")

## 5. 使用示例

### 基础限价单示例
```python
# 外汇EUR/USD买入限价单
order_id = to_order(
    symbol='EURUSD',
    side='B',
    price=1.1000,
    quantity=100000,
    effect=1,  # 开仓
    order_type=2,  # 限价单
    time_in_force=1  # GTC
)
```

### 带止损止盈的黄金交易
```python
# 黄金卖出订单，带止损止盈
order_id = to_order(
    symbol='XAUUSD',
    side='S',
    price=2000.0,
    quantity=1,  # 1手
    effect=1,
    order_type=2,
    stop_loss_price=2010.0,  # 止损价
    take_profit_price=1990.0  # 止盈价
)
```

### 市价单（点击单）示例
```python
# 英镑快速买入市价单
order_id = to_order(
    symbol='GBPUSD',
    side='B',
    quantity=50000,
    effect=1,
    order_type=0,  # 点击单
    time_in_force=5  # FAK - 立即成交剩余撤销
)
```

### 债券交易示例
```python
# 债券限价单，连续匹配模式
order_id = to_order(
    symbol='BOND001',
    side='B',
    price=100.50,
    quantity=1000000,
    effect=1,
    order_type=2,
    bond_quote_type=9,  # 连续匹配
    maturity_date='20251225',
    bp=10  # 容忍10bp滑点
)
```

### 算法交易示例
```python
# SOR智能订单路由
order_id = to_order(
    symbol='EURUSD',
    side='B',
    price=1.1000,
    quantity=500000,
    effect=1,
    order_type=15,  # SOR单
    in_out_market=3,  # 内外部市场
    bp=5  # 容忍5bp滑点
)
```

## 6. 错误处理

### 常见错误情况
1. **订单被拒绝**: 返回 `None` 或 `'0'`
2. **参数验证失败**: 价格、数量等参数不符合要求
3. **市场状态异常**: 市场关闭或暂停交易
4. **风控限制**: 超出持仓限制或风险控制阈值

### 错误处理最佳实践
```python
def safe_place_order(**kwargs):
    """安全下单函数示例"""
    try:
        order_id = to_order(**kwargs)
        
        if order_id is None or order_id == '0':
            print("下单失败，请检查策略日志")
            return None
            
        print(f"下单成功，订单ID: {order_id}")
        return order_id
        
    except Exception as e:
        print(f"下单异常: {str(e)}")
        return None
```

## 7. 注意事项和限制

### 重要注意事项
1. **点击单价格**: 订单类型为点击单(0)时，price参数可以为None
2. **做市交易**: 做市点击时，quantity必须使用当前档位量，否则无法全部成交
3. **时效性限制**: SOR单和点击单的time_in_force字段无效
4. **止损限价单**: 使用止损限价单时必须设置warn_price
5. **掉期交易**: 掉期交易必须设置value_date和maturity_date
6. **逐笔模式**: 逐笔模式平仓需要指定close_order_id
7. **失败处理**: 如果返回None或'0'，说明下单失败，需检查策略日志

### 性能考虑
- 避免频繁下单，建议实现订单缓存机制
- 大批量订单建议分批处理
- 合理设置时效性，避免无效订单占用系统资源

### 风控建议
- 始终设置合理的止损价格
- 控制单笔订单数量，避免过度集中风险
- 定期检查订单状态，及时处理异常订单

## 8. 相关API

### 订单管理相关函数
- `get_order(order_id)`: 获取单个订单详情
- `get_orders()`: 批量获取进行中的委托订单
- `cancel_order(order_id)`: 撤销指定订单

### 典型工作流程
```python
# 1. 下单
order_id = to_order(symbol='EURUSD', side='B', price=1.1000, quantity=100000, effect=1)

# 2. 检查订单状态
if order_id and order_id != '0':
    order_info = get_order(order_id)
    print(f"订单状态: {order_info.get('orderStatus')}")

# 3. 必要时撤单
if need_cancel:
    cancel_order(order_id)
```

### 市场数据配合使用
建议配合行情API使用，获取实时价格信息：
```python
# 获取当前市场价格
market_data = get_market_data(symbol)
bid_price = market_data.bestBid
ask_price = market_data.bestAsk

# 基于市场价格下单
order_id = to_order(
    symbol=symbol,
    side='B',
    price=bid_price * 1.001,  # 略高于买一价
    quantity=quantity,
    effect=1
)
```

---

**文档版本**: v1.0  
**最后更新**: 2024-08-11  
**适用版本**: quantapi 1.3.0+
