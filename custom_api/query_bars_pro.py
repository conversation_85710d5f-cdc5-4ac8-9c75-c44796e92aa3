from datetime import datetime, timezone

import numpy as np
import pandas as pd
import requests


def query_bars_pro(symbol, type_, source, count=-1, fields=None, data_type=1, start_datetime='', end_datetime=''):
    """
    获取一段时间获取N根bar数据
    :param symbol: str 合约唯一代码
    :param type_: str 数据类型, 如1D_BAR_DEPTH
    :param source: str 行情来源
    :param count: int bar数量, 默认-1, 表示全部返回
    :param fields: list 指定返回对象字段, 默认None, 表示全部返回
    :param data_type: int 数据返回类型, 0 表示pandas结构, 1 表示numpy结构, 2 表示dict结构, 默认1
    :param start_datetime: str bar的开始时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回
    :param end_datetime: str bar的结束时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回

    :return: Bar数据, 数据类型根据入参的data_type值而变
    :return: time(int):时间戳
    :return: open(float):开盘价
    :return: close(float):收盘价
    :return: high(float):最高价
    :return: low(float):最低价
    bars = md.query_bars_pro("EURUSDSP", "1D_BAR_DEPTH",  "UBS_HO", count=-1, data_type=DATA_TYPE_PANDAS, start_datetime="2021-12-10 00:00", end_datetime="2021-12-20 00:00")
    """
    # 解析时间参数
    end_time = None
    if end_datetime:
        # 支持多种时间格式
        time_formats = ['%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S']
        for fmt in time_formats:
            try:
                dt = datetime.strptime(end_datetime, fmt)
                end_time = int(dt.timestamp() * 1000)  # 转换为毫秒级时间戳
                break
            except ValueError:
                continue
        if end_time is None:
            raise ValueError(f"无法解析结束时间: {end_datetime}")
    else:
        now_utc = datetime.now(timezone.utc)
        end_time = int(now_utc.timestamp() * 1000)

    # 根据type_参数确定K线周期
    # 这里假设type_参数如"1D_BAR_DEPTH"中的数字代表周期
    if "1D_BAR_DEPTH" in type_:
        bar_type = 1440  # 日线
    elif "1H_BAR_DEPTH" in type_:
        bar_type = 60  # 60分钟线
    elif "5M_BAR_DEPTH" in type_:
        bar_type = 5  # 5分钟线
    elif "1M_BAR_DEPTH" in type_:
        bar_type = 1  # 1分钟线
    else:
        bar_type = 60  # 默认60分钟线

    # 调用financego接口获取数据
    raw_data = _fetch_bars_by_financego(symbol, bar_type, source, count, end_time)

    if raw_data is None:
        return None

    # 处理返回的数据
    if 'data' in raw_data:
        bars_data = raw_data['data']
    else:
        bars_data = []

    # 如果有开始时间限制，过滤数据
    if start_datetime:
        start_time = None
        for fmt in time_formats:
            try:
                dt = datetime.strptime(start_datetime, fmt)
                start_time = int(dt.timestamp() * 1000)  # 转换为毫秒级时间戳
                break
            except ValueError:
                continue

        if start_time is not None:
            # 过滤掉开始时间之前的数据
            bars_data = [bar for bar in bars_data if bar['time'] >= start_time]

    # 强制只保留 time, open, close, high, low 字段
    required_fields = ['time', 'open', 'close', 'high', 'low']
    filtered_bars = []
    for bar in bars_data:
        filtered_bar = {field: bar[field] for field in required_fields if field in bar}
        filtered_bars.append(filtered_bar)
    bars_data = filtered_bars

    # 如果指定了字段，进一步过滤到指定字段（但仍限制在required_fields范围内）
    if fields:
        # 只保留用户指定的字段，但必须在required_fields范围内
        allowed_fields = [field for field in fields if field in required_fields]
        if allowed_fields:
            final_filtered_bars = []
            for bar in bars_data:
                filtered_bar = {field: bar[field] for field in allowed_fields if field in bar}
                final_filtered_bars.append(filtered_bar)
            bars_data = final_filtered_bars

    # 根据data_type参数返回不同格式的数据
    if data_type == 0:  # pandas DataFrame
        if not bars_data:
            return pd.DataFrame()
        df = pd.DataFrame(bars_data)
        # 将time列转换为datetime格式
        df['datetime'] = pd.to_datetime(df['time'], unit='ms')
        return df
    elif data_type == 1:  # numpy结构
        return np.array(bars_data)
    elif data_type == 2:  # dict结构
        return bars_data
    else:  # 默认返回dict结构
        return bars_data


def _fetch_bars_by_financego(symbol, type_, source="MT4", count=-1, end_time=None):
    """
    通过financego接口获取K线数据
    
    :param symbol: 合约代码，如EURUSD
    :param type_: K线类型，如1, 5, 15, 30, 60（分钟）
    :param source: 数据源，如MT4
    :param count: 获取的K线数量，默认-1表示全部
    :param end_time: 结束时间戳（毫秒），默认None表示当前时间
    :return: K线数据列表
    """
    # 根据curl命令分析，构建请求URL和参数
    url = 'https://financego.x-funds.com/infos/ebar/getHisBarListWithCount.action'

    # 处理结束时间，如果未提供则使用当前时间
    if end_time is None:
        import time
        end_time = int(time.time() * 1000)  # 当前时间毫秒级时间戳

    # 构造请求参数
    params = {
        'source': source,
        'symbol': symbol,
        'count': count if count != -1 else 1000,  # 默认获取1000条
        'type': type_,  # K线周期，如60表示60分钟
        'endTime': end_time,
        '_': int(datetime.now().timestamp() * 1000)  # 当前时间戳作为缓存busting参数
    }

    # 设置请求头
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Referer': 'https://financego.x-funds.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'titanOne-client': 'titanOne-client',
        'x-requested-with': 'x-requested-with, XMLHttpRequest'
    }

    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查HTTP错误

        # 解析JSON响应
        data = response.json()
        # 返回数据
        return data
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"数据处理错误: {e}")
        return None


if __name__ == '__main__':
    # 测试代码
    symbol = 'EURUSD'
    type_ = '1D_BAR_DEPTH'
    source = 'MT4'
    count = -1
    start_datetime = None
    end_datetime = None
    bars = query_bars_pro(symbol, type_, source, count)
    print(bars)
